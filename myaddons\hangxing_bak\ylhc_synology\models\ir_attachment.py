# -*- coding: utf-8 -*-
import logging
import base64
import os
from odoo import api, models, fields
from ..services.synology_storage_service_fixed import SynologyStorageService

_logger = logging.getLogger(__name__)

class IrAttachment(models.Model):
    _inherit = 'ir.attachment'

    def _get_synology_service(self):
        """Helper method to get an instance of our Synology service."""
        # 确保群晖存储已启用
        if not self._is_synology_enabled():
            raise Exception("Synology storage is not enabled")
        return SynologyStorageService(self.env)

    def _is_synology_enabled(self):
        """Check if Synology storage is enabled via dedicated parameter."""
        enabled = self.env['ir.config_parameter'].sudo().get_param('ylhc_synology.enable_storage', 'False')
        return enabled.lower() in ('true', '1', 'yes', 'on')

    def _is_synology_filename(self, store_fname):
        """Check if the store_fname matches our Synology filename pattern."""
        if not store_fname:
            return False

        # 我们的群晖文件名格式：{uuid}_{timestamp}_{random}_{process}{extension}
        # 例如：a1b2c3d4e5f6g7h8_1642567890123456_12345_9876.pdf
        import re
        pattern = r'^[a-fA-F0-9]{16}_\d{16}_\d{5}_\d+\.[a-zA-Z0-9]+$'
        return bool(re.match(pattern, store_fname))

    def _should_use_synology_storage(self, vals):
        """
        Determine if this attachment should use Synology storage.
        Uses dedicated parameter instead of ir_attachment.location to avoid system file issues.
        """
        # 首先检查是否启用了群晖存储
        if not self._is_synology_enabled():
            return False

        # 检查文件名和MIME类型，排除系统文件
        name = vals.get('name', '')
        mimetype = vals.get('mimetype', '')

        # 排除系统文件扩展名
        if name:
            name_lower = name.lower()
            system_extensions = ['.js', '.css', '.scss', '.less', '.png', '.jpg', '.jpeg',
                               '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
            if any(name_lower.endswith(ext) for ext in system_extensions):
                return False

        # 排除系统MIME类型
        if mimetype:
            system_mimetypes = [
                'text/javascript', 'application/javascript', 'text/css',
                'image/png', 'image/jpeg', 'image/gif', 'image/svg+xml',
                'image/x-icon', 'font/woff', 'font/woff2', 'application/font-woff'
            ]
            if mimetype in system_mimetypes:
                return False

        # 排除系统模块相关的附件
        res_model = vals.get('res_model', '')
        if res_model in ['ir.ui.view', 'ir.module.module', 'ir.ui.menu', 'ir.actions.report']:
            return False

        # 排除没有关联记录的系统附件（通常是模块资源文件）
        if not vals.get('res_model') or not vals.get('res_id'):
            return False

        # 只处理有实际文件数据的附件
        if not (vals.get('datas') or vals.get('raw')):
            return False

        return True

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override create to handle Synology storage for file uploads.
        Supports both single record and batch creation using @api.model_create_multi.
        The decorator automatically handles single dict -> list conversion.
        """
        _logger.debug(f"IrAttachment.create called with {len(vals_list)} records")

        # 检查是否有需要上传到Synology的文件
        synology_files = []
        for i, vals in enumerate(vals_list):
            if self._should_use_synology_storage(vals) and (vals.get('datas') or vals.get('raw')):
                synology_files.append(i)

        # 如果没有需要上传到Synology的文件，直接使用原生处理
        if not synology_files:
            _logger.debug("No files need Synology storage, using native processing")
            return super(IrAttachment, self).create(vals_list)

        # 为批量上传优化：只创建一次Synology服务连接
        syno_service = None
        if synology_files:
            try:
                syno_service = self._get_synology_service()
                _logger.info(f"Processing {len(synology_files)} files for Synology upload")
            except Exception as e:
                _logger.error(f"Failed to initialize Synology service: {e}")
                # 如果连接失败，所有文件都回退到原生存储
                return super(IrAttachment, self).create(vals_list)

        processed_vals_list = []

        for i, vals in enumerate(vals_list):
            # 如果不是需要上传到Synology的文件，保持原样
            if i not in synology_files:
                processed_vals_list.append(vals)
                continue

            file_data = vals.get('datas') or vals.get('raw')
            try:
                _logger.debug(f"Uploading file to Synology: {vals.get('name', 'unnamed')}")

                # 处理raw字段（二进制数据）需要转换为base64
                if vals.get('raw'):
                    import base64
                    file_data = base64.b64encode(vals['raw']).decode('utf-8')

                # 上传文件到Synology并获取唯一文件名
                original_filename = vals.get('name', 'unnamed_file')
                unique_filename = syno_service.write_file(file_data, original_filename)

                # 创建分享链接
                sharing_url = syno_service.create_sharing_link(unique_filename)

                # 创建修改后的vals副本
                vals_copy = vals.copy()

                if sharing_url:
                    # 修改vals，将附件类型改为URL
                    vals_copy.update({
                        'type': 'url',
                        'url': sharing_url,
                        'store_fname': unique_filename,
                    })
                    _logger.debug(f"File uploaded to Synology successfully: {original_filename}")
                else:
                    _logger.warning(f"Failed to create sharing link for {original_filename}")
                    # 即使分享链接失败，我们也可以保存文件信息，使用binary类型
                    vals_copy.update({
                        'type': 'binary',
                        'store_fname': unique_filename,
                    })

                # 移除datas和raw字段，因为文件已经在Synology上了
                vals_copy.pop('datas', None)
                vals_copy.pop('raw', None)

                processed_vals_list.append(vals_copy)

            except Exception as e:
                _logger.error(f"Error uploading file {vals.get('name', 'unnamed')} to Synology: {e}")
                # 如果上传失败，回退到原生存储
                processed_vals_list.append(vals)

        # 调用父类的create方法处理所有记录
        return super(IrAttachment, self).create(processed_vals_list)

    def unlink(self):
        """
        Override unlink to handle Synology file deletion.
        Collects Synology files before deletion and removes them from NAS.
        """
        if not self._is_synology_enabled():
            return super(IrAttachment, self).unlink()

        # 收集需要从群晖删除的文件
        synology_files_to_delete = []
        for attachment in self:
            # 检查是否是群晖存储的文件
            # 使用严格的检查：store_fname 必须符合我们的群晖文件名格式
            if (attachment.store_fname and
                attachment.type in ['url', 'binary'] and
                self._is_synology_filename(attachment.store_fname)):
                synology_files_to_delete.append(attachment.store_fname)

        # 先调用父类的unlink方法删除Odoo记录
        result = super(IrAttachment, self).unlink()

        # 如果Odoo记录删除成功，再删除群晖上的文件
        if synology_files_to_delete:
            try:
                syno_service = self._get_synology_service()
                _logger.info(f"Deleting {len(synology_files_to_delete)} files from Synology")

                for store_fname in synology_files_to_delete:
                    try:
                        syno_service.delete_file(store_fname)
                        _logger.debug(f"Successfully deleted file from Synology: {store_fname}")
                    except Exception as e:
                        _logger.warning(f"Failed to delete file from Synology: {store_fname}, error: {e}")

            except Exception as e:
                _logger.error(f"Failed to initialize Synology service for deletion: {e}")
                # 即使群晖删除失败，Odoo记录已经删除，不影响结果

        return result

    def write(self, vals):
        """
        Override write to handle Synology storage updates.
        For file data updates: delete old file from Synology and upload new one.
        """
        if not self._is_synology_enabled():
            return super(IrAttachment, self).write(vals)

        # 检查是否有文件数据更新
        file_data = vals.get('datas') or vals.get('raw')
        if not file_data:
            # 没有文件数据更新，使用原生处理
            return super(IrAttachment, self).write(vals)

        _logger.debug(f"Processing write operation for {len(self)} attachments with file data")

        # 处理每个附件的文件更新
        for attachment in self:
            # 检查这个附件是否应该使用群晖存储
            # 创建一个临时vals来检查，包含当前记录的信息
            temp_vals = vals.copy()
            temp_vals.update({
                'name': temp_vals.get('name', attachment.name),
                'mimetype': temp_vals.get('mimetype', attachment.mimetype),
                'res_model': temp_vals.get('res_model', attachment.res_model),
                'res_id': temp_vals.get('res_id', attachment.res_id),
            })

            if not self._should_use_synology_storage(temp_vals):
                continue

            try:
                syno_service = self._get_synology_service()

                # 1. 如果原来是群晖文件，先删除旧文件
                if (attachment.store_fname and
                    attachment.type in ['url', 'binary']):
                    try:
                        syno_service.delete_file(attachment.store_fname)
                        _logger.debug(f"Deleted old file from Synology: {attachment.store_fname}")
                    except Exception as e:
                        _logger.warning(f"Failed to delete old file from Synology: {e}")

                # 2. 处理新文件数据
                current_file_data = file_data
                if vals.get('raw'):
                    import base64
                    current_file_data = base64.b64encode(vals['raw']).decode('utf-8')

                # 3. 上传新文件到群晖
                original_filename = temp_vals.get('name', 'unnamed_file')
                unique_filename = syno_service.write_file(current_file_data, original_filename)

                # 4. 创建分享链接
                sharing_url = syno_service.create_sharing_link(unique_filename)

                # 5. 更新vals，准备写入数据库
                if sharing_url:
                    vals.update({
                        'type': 'url',
                        'url': sharing_url,
                        'store_fname': unique_filename,
                    })
                    _logger.debug(f"File updated on Synology successfully: {original_filename}")
                else:
                    _logger.warning(f"Failed to create sharing link for updated file: {original_filename}")
                    vals.update({
                        'type': 'binary',
                        'store_fname': unique_filename,
                    })

                # 移除datas和raw字段，因为文件已经在群晖上了
                vals.pop('datas', None)
                vals.pop('raw', None)

            except Exception as e:
                _logger.error(f"Error updating file on Synology for attachment {attachment.id}: {e}")
                # 如果群晖操作失败，继续使用原生存储更新

        # 调用父类的write方法
        return super(IrAttachment, self).write(vals)



    def _file_read(self, store_fname):
        """
        Read file from Synology storage.
        Only attempt Synology read for files that were actually stored there.
        """
        # 首先检查是否启用了群晖存储
        if not self._is_synology_enabled():
            return super(IrAttachment, self)._file_read(store_fname)

        # 只有明确标识为群晖文件的才尝试从群晖读取
        # 使用严格的检查条件：
        # 1. type 为 'url' 且 url 包含群晖分享链接 且 store_fname 符合我们的格式
        # 2. 或者 type 为 'binary' 且 store_fname 符合我们的格式
        if (store_fname and hasattr(self, 'type') and
            self._is_synology_filename(store_fname)):

            if (self.type == 'url' and hasattr(self, 'url') and self.url and
                ('sharing' in self.url.lower() or 'synology' in self.url.lower())):
                # URL类型的群晖文件
                try:
                    syno_service = self._get_synology_service()
                    return syno_service.read_file(store_fname)
                except Exception as e:
                    _logger.warning(f"Failed to read file from Synology, falling back to local: {e}")
            elif self.type == 'binary':
                # Binary类型的群晖文件
                try:
                    syno_service = self._get_synology_service()
                    return syno_service.read_file(store_fname)
                except Exception as e:
                    _logger.warning(f"Failed to read file from Synology, falling back to local: {e}")

        # 对于所有其他情况（包括模块图标、系统文件等），使用原生读取
        return super(IrAttachment, self)._file_read(store_fname)