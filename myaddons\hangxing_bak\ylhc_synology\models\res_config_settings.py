# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.exceptions import UserError
import logging

# 导入我们的服务
from ..services.synology_storage_service_fixed import SynologyStorageService, FileStation

_logger = logging.getLogger(__name__)

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # --- 现有字段保持不变 ---
    module_ylhc_synology = fields.Boolean(string="Synology Storage")
    synology_host = fields.Char(string='Synology Host/IP', config_parameter='ylhc_synology.host')
    synology_port = fields.Integer(string='Port', config_parameter='ylhc_synology.port', default=5001)
    synology_user = fields.Char(string='Username', config_parameter='ylhc_synology.user')
    synology_password = fields.Char(string='Password', password=True, config_parameter='ylhc_synology.password')
    synology_use_ssl = fields.Boolean(string='Use SSL (HTTPS)', config_parameter='ylhc_synology.use_ssl', default=False)
    synology_target_share = fields.Char(string='Target Shared Folder', config_parameter='ylhc_synology.target_share', help="The name of the shared folder on Synology to store files, e.g., 'OdooAttachments'.")

    # --- 新增字段：启用群晖存储 ---
    synology_enable_storage = fields.Boolean(
        string='Enable Synology Storage for Attachments',
        config_parameter='ylhc_synology.enable_storage',
        help="When enabled, user-uploaded attachments will be stored on Synology instead of local storage. System files (JS, CSS, images) will still use local storage."
    )

    # --- 新增按钮方法 ---
    def button_test_synology_connection(self):
        """
        Called when the 'Test Connection' button is clicked.
        It tries to connect to Synology and gives feedback to the user.
        """
        # 首先，确保用户已保存了当前设置
        # self.execute() 会保存瞬态模型中的值到系统参数
        self.execute()

        

        if not FileStation:
             raise UserError("The 'synology-api' library is not installed. Please run 'pip install synology-api'.")

        # 从已保存的参数中获取配置
        params = self.env['ir.config_parameter'].sudo()
        host = params.get_param('ylhc_synology.host')
        port = params.get_param('ylhc_synology.port')
        user = params.get_param('ylhc_synology.user')
        password = params.get_param('ylhc_synology.password')
        use_ssl = params.get_param('ylhc_synology.use_ssl')

        if not all([host, port, user, password]):
            raise UserError("Please provide all connection details (Host, Port, Username, Password) and save the settings before testing.")

        try:
            _logger.info(f"Testing connection to Synology at {host}:{port}...")
            # 直接使用库进行一次性连接测试
            fs = FileStation(host, port, user, password, secure=use_ssl, cert_verify=False, dsm_version=7)
            
            # 执行一个简单的、低成本的API调用来验证凭据和连接
            # get_info() 是一个很好的选择
            fs.get_info()

            _logger.info("Synology connection test successful.")
            # 返回一个弹窗通知用户成功
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': "Connection Successful",
                    'message': "Successfully connected to the Synology server.",
                    'type': 'success',
                    'sticky': False,
                }
            }

        except Exception as e:
            _logger.error(f"Synology connection test failed: {e}")
            # 如果失败，抛出一个 UserError，它会以红色错误弹窗的形式显示给用户
            raise UserError(f"Connection Failed: {e}")