# YLHC Synology Storage

This module integrates Synology NAS as a storage backend for Odoo attachments, providing a seamless way to store user-uploaded files on your Synology server while keeping system files local.

## Features

- **Smart Storage**: User attachments go to Synology, system files stay local
- **Complete CRUD Support**: Create, read, update, and delete operations for Synology files
- **Batch Support**: Efficient handling of single and batch attachment operations
- **Automatic Management**: Upload, download, update, and delete operations handled automatically
- **Safe Integration**: No interference with Odoo's core functionality
- **Easy Configuration**: Simple setup through Odoo settings
- **Connection Testing**: Built-in connection verification
- **Performance Optimized**: Single connection for batch operations

## Installation

1. Install the required Python library:
   ```bash
   pip install synology-api==0.7.3
   ```

2. Install this module in Odoo

3. Configure the settings (see Configuration section below)

## Configuration

Go to **Settings > Technical > Synology Storage** and configure:

### Connection Settings
- **Host/IP**: Your Synology NAS IP address or hostname
- **Port**: File Station API port (usually 5001 for HTTPS, 5000 for HTTP)
- **Username**: Synology user account with File Station access
- **Password**: User password
- **Use SSL**: Enable HTTPS connection (recommended)
- **Target Shared Folder**: Folder name to store attachments (e.g., 'OdooAttachments')

### Enable Storage
- **Enable Synology Storage for Attachments**: Toggle this to activate Synology storage

### Test Connection
Click the **Test Connection** button to verify your settings work correctly.

## How It Works

### What Gets Stored on Synology
- User-uploaded documents (PDF, DOC, XLS, etc.)
- Email attachments
- Form attachments with associated records

### What Stays Local
- System files (JS, CSS, SCSS)
- Images used by themes and modules (PNG, JPG, SVG, ICO)
- Font files (WOFF, TTF)
- Module resources and assets
- Attachments without associated records

### File Organization
Files are stored on Synology with unique names to prevent conflicts:
```
/YourSharedFolder/
├── uuid1_timestamp1_random1_process1.pdf
├── uuid2_timestamp2_random2_process2.docx
└── ...
```

## Usage

Once configured and enabled:

1. **Automatic Operation**: All qualifying attachments are automatically stored on Synology
2. **Batch Processing**: Multiple files uploaded simultaneously are handled efficiently
3. **Transparent Access**: Users access files normally through Odoo interface
4. **Sharing Links**: Files get public sharing links for easy access
5. **Update Handling**: File updates delete old versions and upload new ones
6. **Clean Deletion**: Deleting attachments removes files from both Odoo and Synology
7. **Fallback**: If Synology is unavailable, operations fall back to local storage

### API Usage Examples

```python
# Single file creation - automatically handled by @api.model_create_multi
attachment = env['ir.attachment'].create({
    'name': 'document.pdf',
    'datas': base64_encoded_data,
    'res_model': 'res.partner',
    'res_id': partner_id
})

# Batch file creation - native support with optimization
attachments = env['ir.attachment'].create([
    {
        'name': 'doc1.pdf',
        'datas': data1,
        'res_model': 'res.partner',
        'res_id': 1
    },
    {
        'name': 'doc2.docx',
        'datas': data2,
        'res_model': 'res.partner',
        'res_id': 2
    }
])

# Both calls use the same optimized batch processing internally

# File updates - automatically handles old file deletion and new file upload
attachment.write({
    'datas': new_base64_data,
    'name': 'updated_document.pdf'
})

# File deletion - removes from both Odoo and Synology
attachment.unlink()

# Batch deletion - efficiently removes multiple files
attachments.unlink()
```

## Technical Notes

### Advantages Over ir_attachment.location
- **No System Breakage**: Unlike setting `ir_attachment.location = 'synology'`, this approach doesn't break Odoo's web interface
- **Selective Storage**: Only user files go to Synology, system files remain accessible
- **Better Performance**: Web assets load from local storage for optimal performance

### Implementation Details
- **Modern API**: Uses `@api.model_create_multi` decorator for proper batch handling
- **Automatic Conversion**: Single record creation is automatically converted to batch processing
- **Connection Optimization**: Single Synology connection for batch uploads
- **Smart Filtering**: Pre-filters records to identify Synology candidates before connection

### CRUD Operations Handling

#### Create (`create` method)
- Uses `@api.model_create_multi` for batch optimization
- Pre-filters records to identify Synology candidates
- Single connection for multiple file uploads
- Automatic fallback to local storage on errors

#### Read (`_file_read` method)
- Transparent file reading from Synology
- Automatic fallback to local storage if Synology fails
- Maintains compatibility with existing file access patterns

#### Update (`write` method)
- **Smart Update Logic**: Only processes records with file data changes
- **Old File Cleanup**: Automatically deletes previous version from Synology
- **New File Upload**: Uploads updated file with new unique name
- **Atomic Operation**: Ensures consistency between Odoo and Synology

#### Delete (`unlink` method)
- **Batch Deletion**: Efficiently handles multiple file deletions
- **Two-Phase Delete**: First removes Odoo records, then Synology files
- **Error Isolation**: Synology deletion failures don't affect Odoo record removal
- **Cleanup Guarantee**: Ensures no orphaned files remain on Synology

### File Types Excluded
- JavaScript files (`.js`)
- CSS files (`.css`, `.scss`, `.less`)
- Images (`.png`, `.jpg`, `.jpeg`, `.gif`, `.svg`, `.ico`)
- Fonts (`.woff`, `.woff2`, `.ttf`, `.eot`)
- System model attachments (`ir.ui.view`, `ir.module.module`, etc.)

### Error Handling
- Connection failures fall back to local storage
- Failed uploads don't break the attachment creation process
- Comprehensive logging for troubleshooting

## Troubleshooting

### Connection Issues
1. Verify Synology NAS is accessible from Odoo server
2. Check firewall settings on both ends
3. Ensure File Station is enabled on Synology
4. Verify user has File Station permissions
5. **Important**: Ensure "Enable Synology Storage for Attachments" is checked in settings

### Common Issues

#### "Failed to connect or login to Synology" errors when module is disabled
**Problem**: You see connection errors in logs even when Synology storage is not enabled.

**Solution**: This was fixed in the latest version. The module now only attempts connections when:
- Synology storage is explicitly enabled in settings
- A file operation actually requires Synology access

**Check**: Verify that "Enable Synology Storage for Attachments" is unchecked if you don't want to use Synology storage.

### File Access Issues
1. Check shared folder permissions
2. Verify target folder exists and is accessible
3. Review Synology logs for access denials

### Performance Considerations
- Large files may take longer to upload
- Network bandwidth affects upload/download speeds
- Consider using local network connections for best performance
- Batch uploads use a single connection for better efficiency
- Mixed batches (Synology + local files) are handled intelligently

## Version History

- v16.0.1.0.0: Enhanced version with smart filtering
  - Replaced ir_attachment.location dependency
  - Added dedicated enable/disable toggle
  - Enhanced file type filtering
  - Improved system file exclusion
  - Better error handling and fallback
