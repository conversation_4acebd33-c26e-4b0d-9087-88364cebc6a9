# -*- coding: utf-8 -*-
import base64
import logging
from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class TestSynologyStorage(TransactionCase):
    
    def setUp(self):
        super(TestSynologyStorage, self).setUp()
        # 设置测试参数 - 使用新的启用参数而不是ir_attachment.location
        self.env['ir.config_parameter'].sudo().set_param('ylhc_synology.enable_storage', 'True')
        self.env['ir.config_parameter'].sudo().set_param('ylhc_synology.host', 'test.synology.com')
        self.env['ir.config_parameter'].sudo().set_param('ylhc_synology.port', '5001')
        self.env['ir.config_parameter'].sudo().set_param('ylhc_synology.user', 'testuser')
        self.env['ir.config_parameter'].sudo().set_param('ylhc_synology.password', 'testpass')
        self.env['ir.config_parameter'].sudo().set_param('ylhc_synology.use_ssl', 'False')
        self.env['ir.config_parameter'].sudo().set_param('ylhc_synology.target_share', 'OdooAttachments')
        
    def test_should_use_synology_storage(self):
        """测试文件类型过滤逻辑"""
        attachment_model = self.env['ir.attachment']

        # 测试普通文件应该使用Synology存储（需要有datas和关联记录）
        vals_normal = {
            'name': 'test.pdf',
            'mimetype': 'application/pdf',
            'datas': 'dGVzdCBkYXRh',  # base64 encoded 'test data'
            'res_model': 'res.partner',
            'res_id': 1
        }
        self.assertTrue(attachment_model._should_use_synology_storage(vals_normal))

        # 测试JS文件不应该使用Synology存储
        vals_js = {'name': 'test.js', 'mimetype': 'text/javascript', 'datas': 'dGVzdA=='}
        self.assertFalse(attachment_model._should_use_synology_storage(vals_js))

        # 测试CSS文件不应该使用Synology存储
        vals_css = {'name': 'test.css', 'mimetype': 'text/css', 'datas': 'dGVzdA=='}
        self.assertFalse(attachment_model._should_use_synology_storage(vals_css))

        # 测试PNG文件不应该使用Synology存储
        vals_png = {'name': 'test.png', 'mimetype': 'image/png', 'datas': 'dGVzdA=='}
        self.assertFalse(attachment_model._should_use_synology_storage(vals_png))

        # 测试系统模块相关附件不应该使用Synology存储
        vals_module = {'name': 'test.xml', 'res_model': 'ir.module.module', 'datas': 'dGVzdA=='}
        self.assertFalse(attachment_model._should_use_synology_storage(vals_module))

        # 测试没有关联记录的附件不应该使用Synology存储
        vals_no_record = {'name': 'test.pdf', 'mimetype': 'application/pdf', 'datas': 'dGVzdA=='}
        self.assertFalse(attachment_model._should_use_synology_storage(vals_no_record))
        
    def test_filename_generation(self):
        """测试文件名生成的唯一性"""
        from odoo.addons.ylhc_synology.services.synology_storage_service import SynologyStorageService
        
        # 由于无法连接到真实的Synology服务器，我们只测试文件名生成逻辑
        # 这里我们可以模拟一些基本的测试
        
        # 测试扩展名提取
        import os
        filename1 = "test.pdf"
        extension1 = os.path.splitext(filename1)[1].lower()
        self.assertEqual(extension1, '.pdf')
        
        filename2 = "test.document.docx"
        extension2 = os.path.splitext(filename2)[1].lower()
        self.assertEqual(extension2, '.docx')
        
        # 测试无扩展名文件
        filename3 = "README"
        extension3 = os.path.splitext(filename3)[1].lower()
        self.assertEqual(extension3, '')
        
    def test_create_with_fallback(self):
        """测试创建附件时的回退机制"""
        # 由于没有真实的Synology连接，应该回退到原生存储
        test_content = b"Test file content"
        test_data = base64.b64encode(test_content).decode('utf-8')
        
        attachment = self.env['ir.attachment'].create({
            'name': 'test_file.txt',
            'datas': test_data,
            'mimetype': 'text/plain',
        })
        
        # 由于无法连接到Synology，应该回退到原生存储
        # 这里我们主要测试不会抛出异常
        self.assertTrue(attachment.id)
        self.assertEqual(attachment.name, 'test_file.txt')
        
    def test_js_css_exclusion(self):
        """测试JS/CSS文件排除逻辑"""
        test_content = b"console.log('test');"
        test_data = base64.b64encode(test_content).decode('utf-8')
        
        # 创建JS文件附件
        js_attachment = self.env['ir.attachment'].create({
            'name': 'test.js',
            'datas': test_data,
            'mimetype': 'text/javascript',
        })
        
        # JS文件应该使用原生存储，不会转换为URL类型
        self.assertTrue(js_attachment.id)
        self.assertEqual(js_attachment.name, 'test.js')
        
        # 创建CSS文件附件
        css_content = b"body { color: red; }"
        css_data = base64.b64encode(css_content).decode('utf-8')
        
        css_attachment = self.env['ir.attachment'].create({
            'name': 'test.css',
            'datas': css_data,
            'mimetype': 'text/css',
        })
        
        self.assertTrue(css_attachment.id)
        self.assertEqual(css_attachment.name, 'test.css')

    def test_batch_create(self):
        """测试批量创建附件功能"""
        # 准备批量创建的数据
        test_content1 = b"Test file content 1"
        test_data1 = base64.b64encode(test_content1).decode('utf-8')

        test_content2 = b"Test file content 2"
        test_data2 = base64.b64encode(test_content2).decode('utf-8')

        js_content = b"console.log('test');"
        js_data = base64.b64encode(js_content).decode('utf-8')

        vals_list = [
            {
                'name': 'test1.pdf',
                'datas': test_data1,
                'mimetype': 'application/pdf',
                'res_model': 'res.partner',
                'res_id': 1
            },
            {
                'name': 'test2.docx',
                'datas': test_data2,
                'mimetype': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'res_model': 'res.partner',
                'res_id': 2
            },
            {
                'name': 'test.js',
                'datas': js_data,
                'mimetype': 'text/javascript',
            }
        ]

        # 批量创建附件
        attachments = self.env['ir.attachment'].create(vals_list)

        # 验证创建成功
        self.assertEqual(len(attachments), 3)
        self.assertEqual(attachments[0].name, 'test1.pdf')
        self.assertEqual(attachments[1].name, 'test2.docx')
        self.assertEqual(attachments[2].name, 'test.js')

    def test_mixed_batch_create(self):
        """测试混合批量创建（部分使用Synology，部分使用原生存储）"""
        test_content = b"Test file content"
        test_data = base64.b64encode(test_content).decode('utf-8')

        vals_list = [
            # 这个应该使用Synology存储
            {
                'name': 'user_file.pdf',
                'datas': test_data,
                'mimetype': 'application/pdf',
                'res_model': 'res.partner',
                'res_id': 1
            },
            # 这个应该使用原生存储（JS文件）
            {
                'name': 'system.js',
                'datas': test_data,
                'mimetype': 'text/javascript',
            },
            # 这个应该使用原生存储（没有关联记录）
            {
                'name': 'orphan.pdf',
                'datas': test_data,
                'mimetype': 'application/pdf',
            }
        ]

        # 批量创建附件
        attachments = self.env['ir.attachment'].create(vals_list)

        # 验证创建成功
        self.assertEqual(len(attachments), 3)

        # 验证文件名
        names = [att.name for att in attachments]
        self.assertIn('user_file.pdf', names)
        self.assertIn('system.js', names)
        self.assertIn('orphan.pdf', names)

    def test_empty_batch_create(self):
        """测试空列表批量创建"""
        # 测试空列表
        attachments = self.env['ir.attachment'].create([])
        self.assertEqual(len(attachments), 0)

    def test_single_record_compatibility(self):
        """测试单记录创建（@api.model_create_multi 自动处理）"""
        test_content = b"Test file content"
        test_data = base64.b64encode(test_content).decode('utf-8')

        # 使用字典（@api.model_create_multi 会自动转换为列表）
        vals = {
            'name': 'test_single.pdf',
            'datas': test_data,
            'mimetype': 'application/pdf',
            'res_model': 'res.partner',
            'res_id': 1
        }

        # 单记录创建 - 装饰器会自动处理
        attachment = self.env['ir.attachment'].create(vals)

        # 验证创建成功
        self.assertTrue(attachment.id)
        self.assertEqual(attachment.name, 'test_single.pdf')

    def test_batch_create_performance_optimization(self):
        """测试批量创建时的性能优化（只创建一次Synology连接）"""
        # 这个测试主要验证代码逻辑，实际的性能测试需要真实的Synology环境
        test_content = b"Test file content"
        test_data = base64.b64encode(test_content).decode('utf-8')

        # 创建多个需要Synology存储的文件
        vals_list = []
        for i in range(5):
            vals_list.append({
                'name': f'test_{i}.pdf',
                'datas': test_data,
                'mimetype': 'application/pdf',
                'res_model': 'res.partner',
                'res_id': i + 1
            })

        # 批量创建
        attachments = self.env['ir.attachment'].create(vals_list)

        # 验证所有文件都创建成功
        self.assertEqual(len(attachments), 5)
        for i, attachment in enumerate(attachments):
            self.assertEqual(attachment.name, f'test_{i}.pdf')

    def test_unlink_synology_files(self):
        """测试删除群晖存储的附件"""
        test_content = b"Test file content"
        test_data = base64.b64encode(test_content).decode('utf-8')

        # 创建一个群晖存储的附件
        attachment = self.env['ir.attachment'].create({
            'name': 'test_delete.pdf',
            'datas': test_data,
            'mimetype': 'application/pdf',
            'res_model': 'res.partner',
            'res_id': 1
        })

        attachment_id = attachment.id

        # 删除附件
        attachment.unlink()

        # 验证附件已被删除
        deleted_attachment = self.env['ir.attachment'].search([('id', '=', attachment_id)])
        self.assertEqual(len(deleted_attachment), 0)

    def test_write_file_data_update(self):
        """测试更新附件的文件数据"""
        # 创建初始附件
        test_content1 = b"Original file content"
        test_data1 = base64.b64encode(test_content1).decode('utf-8')

        attachment = self.env['ir.attachment'].create({
            'name': 'test_update.pdf',
            'datas': test_data1,
            'mimetype': 'application/pdf',
            'res_model': 'res.partner',
            'res_id': 1
        })

        # 更新文件数据
        test_content2 = b"Updated file content"
        test_data2 = base64.b64encode(test_content2).decode('utf-8')

        attachment.write({
            'datas': test_data2,
            'name': 'test_update_new.pdf'
        })

        # 验证更新成功
        self.assertEqual(attachment.name, 'test_update_new.pdf')

    def test_write_metadata_only(self):
        """测试只更新元数据（不更新文件数据）"""
        test_content = b"Test file content"
        test_data = base64.b64encode(test_content).decode('utf-8')

        attachment = self.env['ir.attachment'].create({
            'name': 'test_metadata.pdf',
            'datas': test_data,
            'mimetype': 'application/pdf',
            'res_model': 'res.partner',
            'res_id': 1
        })

        # 只更新元数据
        attachment.write({
            'name': 'test_metadata_updated.pdf',
            'res_id': 2
        })

        # 验证元数据更新成功
        self.assertEqual(attachment.name, 'test_metadata_updated.pdf')
        self.assertEqual(attachment.res_id, 2)

    def test_batch_unlink(self):
        """测试批量删除附件"""
        test_content = b"Test file content"
        test_data = base64.b64encode(test_content).decode('utf-8')

        # 创建多个附件
        attachments = self.env['ir.attachment'].create([
            {
                'name': f'test_batch_delete_{i}.pdf',
                'datas': test_data,
                'mimetype': 'application/pdf',
                'res_model': 'res.partner',
                'res_id': i + 1
            }
            for i in range(3)
        ])

        attachment_ids = attachments.ids

        # 批量删除
        attachments.unlink()

        # 验证所有附件都被删除
        remaining_attachments = self.env['ir.attachment'].search([('id', 'in', attachment_ids)])
        self.assertEqual(len(remaining_attachments), 0)
