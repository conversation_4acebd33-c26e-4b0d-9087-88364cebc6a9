<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_config_settings_view_form_synology" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.synology</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="90"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('settings')]" position="inside">
                    <div class="app_settings_block" data-string="Synology Storage" string="Synology Storage" data-key="ylhc_synology" groups="base.group_system">
                        <h2>Synology Attachment Storage</h2>
                        <div class="row mt16 o_settings_container" name="synology_setting_container">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="synology_enable_storage"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="synology_enable_storage"/>
                                    <div class="text-muted">
                                        When enabled, user-uploaded attachments will be stored on Synology.
                                        System files (JS, CSS, images) will continue to use local storage.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h3 class="mt16">Connection Details</h3>
                        <div class="row mt16 o_settings_container" name="synology_connection_container">
                             <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_right_pane">
                                    <label for="synology_host"/>
                                    <div class="text-muted">
                                        Hostname or IP Address of your Synology NAS.
                                    </div>
                                    <div class="content-group">
                                        <field name="synology_host"/>
                                    </div>
                                    <label for="synology_port" class="mt16"/>
                                    <div class="text-muted">
                                        File Station API port (default: 5001 for SSL).
                                    </div>
                                    <div class="content-group">
                                        <field name="synology_port"/>
                                    </div>
                                    <label for="synology_use_ssl" class="mt16"/>
                                    <div class="content-group">
                                        <div class="mt8">
                                            <field name="synology_use_ssl"/>
                                            <span>Use SSL (HTTPS)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_right_pane">
                                    <label for="synology_user"/>
                                    <div class="content-group">
                                        <field name="synology_user"/>
                                    </div>
                                    <label for="synology_password" class="mt16"/>
                                    <div class="content-group">
                                        <field name="synology_password"/>
                                    </div>
                                    <label for="synology_target_share" class="mt16"/>
                                     <div class="text-muted">
                                        Shared folder name for storing attachments.
                                    </div>
                                    <div class="content-group">
                                        <field name="synology_target_share"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>

                <xpath expr="//div[hasclass('app_settings_block')][@data-key='ylhc_synology']//div[contains(@name, 'synology_connection_container')]" position="after">
                    <div class="row mt16">
                        <div class="col-12 col-lg-6 o_setting_box">
                           <div class="o_setting_right_pane">
                               <button name="button_test_synology_connection"
                                   type="object"
                                   class="btn-primary"
                                   string="Test Connection"/>
                               <div class="text-muted">
                                   Click to verify the connection details with your Synology server.
                                   Please save settings before testing.
                               </div>
                           </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>