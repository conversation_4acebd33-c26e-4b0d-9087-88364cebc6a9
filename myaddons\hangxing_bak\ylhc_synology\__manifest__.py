# -*- coding: utf-8 -*-
{
    'name': "YLHC Synology Storage",
    'summary': """
        Integrate Synology server as a storage backend for Odoo attachments.
    """,
    'description': """
        This module allows Odoo to store file attachments directly onto a 
        Synology server instead of the local filesystem or database.
        It extends the ir.attachment model and uses a configurable storage service.
    """,
    'author': "ylhctec",
    'website': "https://www.ylhctec.com",
    'category': 'Technical/File Storage',
    'version': '********.0',
    'depends': ['base'],
    'data': [
        'views/res_config_settings_views.xml',
    ],
    'test': [
        'tests/test_synology_storage.py',
    ],
    'external_dependencies': {
        'python': ['synology_api == 0.7.3'],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}