# -*- coding: utf-8 -*-
import logging
import base64
import uuid
import os
import tempfile
import time
import random
from odoo import models, exceptions

# 处理synology-api库的兼容性问题
try:
    from synology_api.filestation import FileStation
except ImportError:
    FileStation = None

_logger = logging.getLogger(__name__)

class SynologyStorageService:
    def __init__(self, env):
        self.env = env
        self.fs = None
        # 不在初始化时自动连接，而是在需要时才连接

    def _get_target_path(self, filename=None):
        """获取目标路径，支持子文件夹配置"""
        params = self.env['ir.config_parameter'].sudo()
        target_share = params.get_param('ylhc_synology.target_share')
        if not target_share:
            raise exceptions.UserError("Synology target share not configured.")
        
        # 确保路径以/开头
        base_path = target_share if target_share.startswith('/') else f"/{target_share}"
        
        if filename:
            return f"{base_path}/{filename}"
        return base_path

    def _ensure_connected(self):
        """确保连接到群晖，如果还没有连接的话"""
        if self.fs is not None:
            return  # 已经连接

        if not FileStation:
            raise exceptions.UserError("The 'synology-api' library is not installed. Please install it with: pip install synology-api")

        params = self.env['ir.config_parameter'].sudo()
        host, port, user, password, use_ssl = (
            params.get_param('ylhc_synology.host'),
            params.get_param('ylhc_synology.port'),
            params.get_param('ylhc_synology.user'),
            params.get_param('ylhc_synology.password'),
            params.get_param('ylhc_synology.use_ssl')
        )
        if not all([host, port, user, password]):
            raise exceptions.UserError("Synology connection details are not fully configured.")

        try:
            self.fs = FileStation(host, port, user, password, secure=use_ssl, cert_verify=False, dsm_version=7)
            self.fs.get_list_share()
            _logger.info("Successfully connected to Synology File Station.")
        except Exception as e:
            _logger.error(f"Failed to connect or login to Synology: {e}")
            self.fs = None
            raise exceptions.UserError(f"Failed to connect to Synology: {e}")

    def write_file(self, base64_data, original_filename=''):
        """
        Upload file to Synology with enhanced unique filename generation.
        Generates highly unique filename to prevent conflicts and overwrites.
        """
        self._ensure_connected()
        if not self.fs:
            raise exceptions.UserError("Not connected to Synology. Check configuration.")
        
        try:
            file_content = base64.b64decode(base64_data)
        except Exception as e:
            raise exceptions.UserError(f"Invalid base64 data: {e}")
            
        # 提取文件扩展名
        extension = ''
        if original_filename and '.' in original_filename:
            extension = os.path.splitext(original_filename)[1].lower()
            # 限制扩展名长度，防止异常长的扩展名
            if len(extension) > 10:
                extension = extension[:10]

        # 增强的唯一文件名生成策略
        uuid_part = str(uuid.uuid4()).replace('-', '')[:16]  # 取UUID的前16位
        timestamp_part = str(int(time.time() * 1000000))  # 微秒级时间戳
        random_part = str(random.randint(10000, 99999))  # 5位随机数
        process_part = str(os.getpid())  # 进程ID
        
        unique_filename = f"{uuid_part}_{timestamp_part}_{random_part}_{process_part}{extension}"
        
        # 确保文件名不会过长
        if len(unique_filename) > 200:
            uuid_part = uuid_part[:8]
            unique_filename = f"{uuid_part}_{timestamp_part}_{random_part}_{process_part}{extension}"

        # 使用新的路径方法
        destination_path = self._get_target_path()

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_local_path = os.path.join(temp_dir, unique_filename)
            try:
                # 写入临时文件
                with open(temp_local_path, 'wb') as temp_f:
                    temp_f.write(file_content)

                # 上传到Synology
                _logger.info(f"Uploading file to Synology: {unique_filename} (size: {len(file_content)} bytes)")
                _logger.info(f"Destination path: {destination_path}")
                
                self.fs.upload_file(destination_path, temp_local_path, create_parents=True, overwrite=False)
                
                # 暂时跳过验证步骤，避免权限问题
                _logger.info(f"File uploaded to Synology: {unique_filename}")
                return unique_filename

            except Exception as e:
                _logger.error(f"Failed to upload file to Synology: {e}", exc_info=True)
                raise exceptions.UserError(f"Synology Upload Error: {e}")
            
    def create_sharing_link(self, store_fname):
        """Creates a public sharing link for a file on Synology."""
        _logger.info(f"create_sharing_link called with store_fname: {store_fname}")

        self._ensure_connected()
        if not self.fs:
            _logger.error("create_sharing_link: self.fs is None, connection failed")
            return None

        try:
            full_path = self._get_target_path(store_fname)
            _logger.info(f"Creating sharing link for: {full_path}")

            response = self.fs.create_sharing_link(path=full_path)
            _logger.info(f"Sharing link API response: {response}")

            if response and response.get('data', {}).get('links'):
                sharing_url = response['data']['links'][0].get('url')
                _logger.info(f"Sharing link created: {sharing_url}")
                return sharing_url
            else:
                _logger.warning(f"No sharing links found in response: {response}")
                return None
        except Exception as e:
            _logger.error(f"Exception creating sharing link for {store_fname}: {e}", exc_info=True)
            return None

    def read_file(self, store_fname):
        """
        Reads a file from Synology by downloading it to a temporary location first.
        """
        if not store_fname:
            return b''

        self._ensure_connected()
        if not self.fs:
            return b''
        
        try:
            full_path = self._get_target_path(store_fname)
            with tempfile.TemporaryDirectory() as temp_dir:
                self.fs.get_file(path=full_path, mode='download', dest_path=temp_dir)
                downloaded_filepath = os.path.join(temp_dir, store_fname)
                if os.path.exists(downloaded_filepath):
                    with open(downloaded_filepath, 'rb') as f:
                        return f.read()
                return b''
        except Exception as e:
            _logger.error(f"Exception in read_file for {store_fname}: {e}", exc_info=True)
            return b''

    def delete_file(self, store_fname):
        """Deletes a file from Synology using the correct method."""
        self._ensure_connected()
        if not self.fs:
            return False
        
        try:
            full_path = self._get_target_path(store_fname)
            _logger.info(f"Deleting file from Synology: {full_path}")
            self.fs.delete_blocking_function(full_path)
            return True
        except Exception as e:
            _logger.error(f"Failed to delete file from Synology ({store_fname}): {e}")
            return False
